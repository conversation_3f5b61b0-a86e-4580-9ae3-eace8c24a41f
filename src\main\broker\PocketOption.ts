import { io, Socket } from 'socket.io-client'
import { extractAuth } from '../../shared/utils/auth'
import { Region } from '../../shared/regions'
import { logger } from '../../shared/utils/logger'
import { BrowserWindow } from 'electron'
import {
  expiryToSeconds,
  formatChartTimeframe,
  formatData,
  timeframeToOffset
} from '../../shared/utils/formatters'

// Import constants and enums
import {
  ConnectionState,
  HeartbeatHealth,
  CONNECTION_CONFIG,
  HEARTBEAT_CONFIG,
  SOCKET_EVENTS,
  BROADCAST_EVENTS,
  AUTO_RECONNECT_REASONS
} from '../../shared/constants/broker'

/**
 * PocketOption broker class implementing Singleton pattern for managing WebSocket connections
 * to PocketOption trading platform. Handles authentication, connection management, heartbeat
 * monitoring, and event broadcasting to Electron renderer processes.
 */
export class PocketOption {
  private static _instance: PocketOption | null = null
  private socket: Socket | null = null
  private connectionState: ConnectionState = ConnectionState.DISCONNECTED
  private reconnectAttempts: number = 0
  private reconnectTimer: NodeJS.Timeout | null = null

  // Heartbeat management
  private heartbeatInterval: NodeJS.Timeout | null = null
  private heartbeatTimeout: NodeJS.Timeout | null = null
  private heartbeatHealth: HeartbeatHealth = HeartbeatHealth.STOPPED
  private missedHeartbeats: number = 0
  private lastHeartbeatSent: number = 0
  private lastHeartbeatReceived: number = 0

  private isBotRunning: boolean = false
  private chartSettings: ChartSettings | null = null

  /**
   * Private constructor to enforce Singleton pattern
   * @param ssID - Session ID for authentication
   * @param isDemo - Whether to connect to demo environment
   */
  private constructor(
    private readonly ssID: string,
    private readonly isDemo: boolean
  ) {
    this.validateConstructorParams()
  }

  /**
   * Validates constructor parameters
   * @throws Error if parameters are invalid
   */
  private validateConstructorParams(): void {
    if (!this.ssID || typeof this.ssID !== 'string') {
      throw new Error('Session ID must be a non-empty string')
    }
    if (typeof this.isDemo !== 'boolean') {
      throw new Error('isDemo must be a boolean value')
    }
  }

  /**
   * Gets the singleton instance of PocketOption
   * @param ssID - Session ID for authentication
   * @param isDemo - Whether to connect to demo environment
   * @returns The singleton instance
   */
  public static getInstance(ssID: string, isDemo: boolean): PocketOption {
    if (!PocketOption._instance) {
      PocketOption._instance = new PocketOption(ssID, isDemo)
    }
    return PocketOption._instance
  }

  /**
   * Destroys the singleton instance (useful for testing or cleanup)
   */
  public static destroyInstance(): void {
    if (PocketOption._instance) {
      PocketOption._instance.disconnect()
      PocketOption._instance = null
    }
  }

  /**
   * Gets the current connection state
   * @returns Current connection state
   */
  public getConnectionState(): ConnectionState {
    return this.connectionState
  }

  /**
   * Gets the current heartbeat health status
   * @returns Current heartbeat health status
   */
  public getHeartbeatHealth(): HeartbeatHealth {
    return this.heartbeatHealth
  }

  /**
   * Checks if the socket is currently connected
   * @returns True if connected, false otherwise
   */
  public isConnected(): boolean {
    return this.socket?.connected === true && this.connectionState === ConnectionState.CONNECTED
  }

  /**
   * Gets heartbeat statistics
   * @returns Object containing heartbeat statistics
   */
  public getHeartbeatStats(): HeartbeatStats {
    return {
      health: this.heartbeatHealth,
      missedBeats: this.missedHeartbeats,
      lastSent: this.lastHeartbeatSent,
      lastReceived: this.lastHeartbeatReceived,
      isActive: this.heartbeatInterval !== null
    }
  }

  /**
   * Establishes connection to PocketOption WebSocket server
   * @throws Error if authentication data is invalid
   */
  public async connect(): Promise<void> {
    try {
      if (this.isConnected()) {
        logger.warn('Broker', 'Already connected to PocketOption')
        return
      }

      this.setConnectionState(ConnectionState.CONNECTING)

      const endpoint = this.getEndpoint()
      const options = Region.SOCKET_OPTIONS

      logger.info('Broker', `Connecting to ${endpoint}`)

      this.socket = io(endpoint, options)
      this.setupSocketEventHandlers()
    } catch (error) {
      this.setConnectionState(ConnectionState.ERROR)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      logger.error('Broker', `Connection Error: ${errorMessage}`)
      throw new Error(`Failed to connect to PocketOption: ${errorMessage}`)
    }
  }

  /**
   * Disconnects from the WebSocket server and cleans up resources
   */
  public disconnect(): void {
    try {
      this.clearReconnectTimer()
      this.stopHeartbeat()

      if (this.socket) {
        this.socket.removeAllListeners()
        this.socket.disconnect()
        this.socket = null
      }

      this.setConnectionState(ConnectionState.DISCONNECTED)
      this.reconnectAttempts = 0

      logger.info('Broker', 'Disconnected from PocketOption')
      this.broadcast(BROADCAST_EVENTS.DISCONNECTED)
    } catch (error) {
      const errorMessage = this.getErrorMessage(error)
      logger.error('Broker', `Disconnect Error: ${errorMessage}`)
    }
  }

  /**
   * Attempts to reconnect to the WebSocket server
   */
  public async reconnect(): Promise<void> {
    if (this.connectionState === ConnectionState.RECONNECTING) {
      logger.warn('Broker', 'Reconnection already in progress')
      return
    }

    if (this.reconnectAttempts >= CONNECTION_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      logger.error(
        'Broker',
        `Max reconnection attempts (${CONNECTION_CONFIG.MAX_RECONNECT_ATTEMPTS}) reached`
      )
      this.setConnectionState(ConnectionState.ERROR)
      return
    }

    this.setConnectionState(ConnectionState.RECONNECTING)
    this.stopHeartbeat() // Stop heartbeat during reconnection
    this.reconnectAttempts++

    const delay = this.calculateReconnectDelay()

    logger.info(
      'Broker',
      `Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts}/${CONNECTION_CONFIG.MAX_RECONNECT_ATTEMPTS})`
    )

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect()
      } catch (error) {
        const errorMessage = this.getErrorMessage(error)
        logger.error('Broker', `Reconnection failed: ${errorMessage}`)

        if (this.reconnectAttempts < CONNECTION_CONFIG.MAX_RECONNECT_ATTEMPTS) {
          await this.reconnect()
        } else {
          this.setConnectionState(ConnectionState.ERROR)
        }
      }
    }, delay)
  }

  public async startBot(): Promise<void> {
    try {
      if (!this.socket) {
        logger.error('Broker', 'Socket not connected')
        return
      }

      if (this.isBotRunning) return
      this.isBotRunning = true

      // 1. Get the chart settings
      const chartSettings = this.chartSettings
      if (!chartSettings) {
        logger.error('Broker', 'Chart settings not found')
        return
      }

      // 2. Format the chart settings
      const chartTimeframe = formatChartTimeframe(chartSettings.chartPeriod as number)
      const period = expiryToSeconds(chartTimeframe as string)
      const offset = timeframeToOffset(chartTimeframe as string)
      const asset = chartSettings.symbol as string

      // 3. Load the chart history
      const rand = Math.floor(Math.random() * 90 + 10).toString() // Random number between 10 and 100
      const currentUnix = Math.floor(Date.now() / 1000) // Current time in Unix timestamp format
      const t = (currentUnix + 2 * 60 * 60).toString() // 2 hours from now
      const index = parseInt(t + rand, 10) // Create a unique index for the order

      logger.info(
        'Broker',
        `Starting bot with settings: ${JSON.stringify(
          {
            chartTimeframe,
            period,
            offset,
            asset,
            index,
            currentUnix,
            t
          },
          null,
          2
        )}`
      )

      // 4. Send the necessary socket events to trigger `updateStream`, `updateHistoryNewFast`, and `loadHistoryPeriodFast` events
      this.socket.emit('changeSymbol', { asset, period })
      this.socket.emit('loadHistoryPeriod', {
        asset,
        index,
        offset,
        period,
        time: currentUnix
      })
    } catch (error) {
      logger.error('Broker', `Failed to start bot: ${this.getErrorMessage(error)}`)
    }
  }

  /**
   * Calculates reconnection delay using exponential backoff
   * @returns Delay in milliseconds
   */
  private calculateReconnectDelay(): number {
    const exponentialDelay =
      CONNECTION_CONFIG.RECONNECT_BASE_DELAY * Math.pow(2, this.reconnectAttempts - 1)
    return Math.min(exponentialDelay, CONNECTION_CONFIG.MAX_RECONNECT_DELAY)
  }

  /**
   * Extracts error message from unknown error type
   * @param error - Error of unknown type
   * @returns Error message string
   */
  private getErrorMessage(error: unknown): string {
    return error instanceof Error ? error.message : 'Unknown error'
  }

  /**
   * Gets the appropriate endpoint based on demo mode
   * @returns WebSocket endpoint URL
   */
  private getEndpoint(): string {
    return this.isDemo ? Region.DEMO_REGION : Region.getRegion(true)[0]
  }

  /**
   * Sets up all socket event handlers
   */
  private setupSocketEventHandlers(): void {
    if (!this.socket) return

    this.socket.on(SOCKET_EVENTS.CONNECT, this.handleConnect.bind(this))
    this.socket.on(SOCKET_EVENTS.DISCONNECT, this.handleDisconnect.bind(this))
    this.socket.on(SOCKET_EVENTS.CONNECT_ERROR, this.handleConnectError.bind(this))
    this.socket.on(SOCKET_EVENTS.SUCCESS_AUTH, this.handleSuccessAuth.bind(this))
    this.socket.on(SOCKET_EVENTS.GET_BALANCE, this.handleBalanceUpdate.bind(this))
    this.socket.on(SOCKET_EVENTS.CHART, this.handleChartUpdate.bind(this))
    this.socket.on(SOCKET_EVENTS.STREAM, this.handleStreamUpdate.bind(this))
    this.socket.on(SOCKET_EVENTS.ERROR, this.handleError.bind(this))
    this.socket.on(SOCKET_EVENTS.PONG, this.handleHeartbeatPong.bind(this))
  }

  /**
   * Handles socket connection event
   */
  private handleConnect(): void {
    logger.info('Broker', 'Socket connected, authenticating...')

    try {
      const authData = this.extractAuthData()
      const authPayload = this.createAuthPayload(authData)

      this.socket?.emit(SOCKET_EVENTS.AUTH, authPayload)
    } catch (error) {
      const errorMessage = this.getErrorMessage(error)
      logger.error('Broker', `Authentication failed: ${errorMessage}`)
      this.setConnectionState(ConnectionState.ERROR)
    }
  }

  /**
   * Handles socket disconnection event
   * @param reason - Reason for disconnection
   */
  private handleDisconnect(reason: string): void {
    logger.warn('Broker', `Socket disconnected: ${reason}`)
    this.stopHeartbeat() // Stop heartbeat on disconnect
    this.setConnectionState(ConnectionState.DISCONNECTED)
    this.broadcast(BROADCAST_EVENTS.DISCONNECTED, { reason })

    // Auto-reconnect for certain disconnect reasons
    if (this.shouldAutoReconnect(reason)) {
      this.reconnect().catch((error) => {
        logger.error('Broker', `Auto-reconnect failed: ${this.getErrorMessage(error)}`)
      })
    }
  }

  /**
   * Handles socket connection error event
   * @param error - Connection error
   */
  private handleConnectError(error: Error): void {
    logger.error('Broker', `Connection error: ${error.message}`)
    this.stopHeartbeat() // Stop heartbeat on connection error
    this.setConnectionState(ConnectionState.ERROR)
    this.broadcast(BROADCAST_EVENTS.CONNECTION_ERROR, { error: error.message })
  }

  /**
   * Handles successful authentication event
   */
  private handleSuccessAuth(): void {
    logger.success('Broker', 'Successfully authenticated with PocketOption')
    this.setConnectionState(ConnectionState.CONNECTED)
    this.reconnectAttempts = 0 // Reset reconnect attempts on successful connection
    this.startHeartbeat() // Start heartbeat after successful authentication
    this.broadcast(BROADCAST_EVENTS.CONNECTED)
  }

  /**
   * Handles balance update event
   * @param args - Event arguments
   */
  private handleBalanceUpdate(...args: unknown[]): void {
    const data = this.formatBroadcastPayload(args) as Balance

    this.broadcast(BROADCAST_EVENTS.ACCOUNT_BALANCE, data.balance)
  }

  /**
   * Handles chart update event
   * @param args - Event arguments
   */
  private handleChartUpdate(...args: unknown[]): void {
    const data = this.formatBroadcastPayload(args) as ChartData

    let settings: ChartSettings | null = null
    if (data.settings) {
      settings = JSON.parse(data.settings)
    }

    this.chartSettings = settings
  }

  private handleStreamUpdate(...args: unknown[]): void {
    const data = this.formatBroadcastPayload(args)
    logger.trade(`Stream update: ${JSON.stringify(data, null, 2)}`)
  }

  /**
   * Handles general socket error event
   * @param error - Socket error
   */
  private handleError(error: Error): void {
    logger.error('Broker', `Socket error: ${error.message}`)
    this.broadcast(BROADCAST_EVENTS.ERROR, { error: error.message })
  }

  /**
   * Determines if auto-reconnection should be attempted based on disconnect reason
   * @param reason - Disconnect reason
   * @returns True if auto-reconnection should be attempted
   */
  private shouldAutoReconnect(reason: string): boolean {
    return (AUTO_RECONNECT_REASONS as readonly string[]).includes(reason)
  }

  /**
   * Extracts and validates authentication data from session ID
   * @returns Validated authentication data
   * @throws Error if authentication data is invalid
   */
  private extractAuthData(): AuthData {
    const authData = extractAuth(this.ssID)

    if (!authData || !authData.session || !authData.uid) {
      throw new Error('Invalid authentication data: missing session or uid')
    }

    return authData
  }

  /**
   * Creates authentication payload for socket
   * @param authData - Authentication data
   * @returns Socket authentication payload
   */
  private createAuthPayload(authData: AuthData): SocketAuthPayload {
    return {
      isDemo: this.isDemo ? CONNECTION_CONFIG.DEMO_MODE_FLAG : CONNECTION_CONFIG.LIVE_MODE_FLAG,
      isFastHistory: true,
      platform: CONNECTION_CONFIG.PLATFORM_ID,
      session: authData.session,
      uid: authData.uid
    }
  }

  /**
   * Sets the connection state and logs the change
   * @param state - New connection state
   */
  private setConnectionState(state: ConnectionState): void {
    if (this.connectionState !== state) {
      const previousState = this.connectionState
      this.connectionState = state
      logger.debug('Broker', `Connection state changed: ${previousState} -> ${state}`)
      this.broadcast(BROADCAST_EVENTS.STATE_CHANGE, {
        from: previousState,
        to: state,
        timestamp: Date.now()
      })
    }
  }

  /**
   * Clears the reconnection timer if it exists
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * Broadcasts events to all Electron renderer processes
   * @param event - Event name to broadcast
   * @param data - Optional data to send with the event
   */
  private broadcast(event: string, data?: unknown): void {
    try {
      const windows = this.getActiveWindows()

      if (windows.length === 0) {
        logger.debug('Broker', 'No windows available for broadcasting')
        return
      }

      const payload = this.formatBroadcastPayload(data)

      this.sendToWindows(windows, event, payload)

      logger.debug('Broker', `Broadcasted event '${event}' to ${windows.length} window(s)`)
    } catch (error) {
      const errorMessage = this.getErrorMessage(error)
      logger.error('Broker', `Broadcast error: ${errorMessage}`)
    }
  }

  /**
   * Gets all active (non-destroyed) browser windows
   * @returns Array of active browser windows
   */
  private getActiveWindows(): BrowserWindow[] {
    return BrowserWindow.getAllWindows().filter((window) => !window.isDestroyed())
  }

  /**
   * Formats broadcast payload data
   * @param data - Raw data to format
   * @returns Formatted payload
   */
  private formatBroadcastPayload(data?: unknown): unknown {
    if (!data) return data

    // Format data if it's an array
    if (Array.isArray(data)) {
      const formatted = formatData(data)
      return Array.isArray(formatted) && formatted.length === 1 ? formatted[0] : formatted
    }

    return data
  }

  /**
   * Sends event to all provided windows
   * @param windows - Array of browser windows
   * @param event - Event name
   * @param payload - Event payload
   */
  private sendToWindows(windows: BrowserWindow[], event: string, payload: unknown): void {
    windows.forEach((window) => {
      try {
        window.webContents.send('broker:event', event, payload)
      } catch (error) {
        logger.warn('Broker', `Failed to send event to window: ${this.getErrorMessage(error)}`)
      }
    })
  }

  /**
   * Starts the heartbeat mechanism
   */
  private startHeartbeat(): void {
    this.stopHeartbeat() // Clear any existing heartbeat

    this.setHeartbeatHealth(HeartbeatHealth.HEALTHY)
    this.missedHeartbeats = 0

    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat()
    }, HEARTBEAT_CONFIG.interval)

    logger.debug('Broker', `Heartbeat started with ${HEARTBEAT_CONFIG.interval}ms interval`)
  }

  /**
   * Stops the heartbeat mechanism
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }

    this.setHeartbeatHealth(HeartbeatHealth.STOPPED)
    this.missedHeartbeats = 0

    logger.debug('Broker', 'Heartbeat stopped')
  }

  /**
   * Sends a heartbeat ping to the server
   */
  private sendHeartbeat(): void {
    if (!this.socket || !this.socket.connected) {
      logger.warn('Broker', 'Cannot send heartbeat: socket not connected')
      this.handleHeartbeatFailure()
      return
    }

    this.lastHeartbeatSent = Date.now()
    this.socket.emit(SOCKET_EVENTS.PING)

    // Set timeout for pong response
    this.heartbeatTimeout = setTimeout(() => {
      this.handleHeartbeatTimeout()
    }, HEARTBEAT_CONFIG.timeout)

    this.broadcast(BROADCAST_EVENTS.HEARTBEAT_SENT, {
      timestamp: this.lastHeartbeatSent,
      missedBeats: this.missedHeartbeats
    })

    logger.debug('Broker', 'Heartbeat ping sent')
  }

  /**
   * Handles heartbeat pong response
   */
  private handleHeartbeatPong(): void {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }

    this.lastHeartbeatReceived = Date.now()
    this.missedHeartbeats = 0

    const responseTime = this.lastHeartbeatReceived - this.lastHeartbeatSent

    this.setHeartbeatHealth(HeartbeatHealth.HEALTHY)

    this.broadcast(BROADCAST_EVENTS.HEARTBEAT_RECEIVED, {
      timestamp: this.lastHeartbeatReceived,
      responseTime,
      missedBeats: this.missedHeartbeats
    })

    logger.debug('Broker', `Heartbeat pong received (${responseTime}ms)`)
  }

  /**
   * Handles heartbeat timeout
   */
  private handleHeartbeatTimeout(): void {
    this.missedHeartbeats++

    logger.warn(
      'Broker',
      `Heartbeat timeout (missed: ${this.missedHeartbeats}/${HEARTBEAT_CONFIG.maxMissedBeats})`
    )

    if (this.missedHeartbeats >= HEARTBEAT_CONFIG.maxMissedBeats) {
      this.handleHeartbeatFailure()
    } else {
      this.setHeartbeatHealth(HeartbeatHealth.DEGRADED)
    }
  }

  /**
   * Handles heartbeat failure
   */
  private handleHeartbeatFailure(): void {
    this.setHeartbeatHealth(HeartbeatHealth.UNHEALTHY)

    logger.error('Broker', `Heartbeat failed after ${this.missedHeartbeats} missed beats`)

    this.broadcast(BROADCAST_EVENTS.HEARTBEAT_FAILED, {
      missedBeats: this.missedHeartbeats,
      maxMissedBeats: HEARTBEAT_CONFIG.maxMissedBeats
    })

    // Trigger reconnection
    this.reconnect().catch((error) => {
      logger.error(
        'Broker',
        `Heartbeat failure reconnection failed: ${this.getErrorMessage(error)}`
      )
    })
  }

  /**
   * Sets heartbeat health status and broadcasts change
   * @param health - New heartbeat health status
   */
  private setHeartbeatHealth(health: HeartbeatHealth): void {
    if (this.heartbeatHealth !== health) {
      const previousHealth = this.heartbeatHealth
      this.heartbeatHealth = health

      logger.debug('Broker', `Heartbeat health changed: ${previousHealth} -> ${health}`)

      this.broadcast(BROADCAST_EVENTS.HEARTBEAT_HEALTH_CHANGE, {
        from: previousHealth,
        to: health,
        missedBeats: this.missedHeartbeats
      })
    }
  }
}
